from sqlalchemy.orm import Session
from datetime import datetime
from app.models.organization import Organization
from app.models.user import User
from app.schemas.organization import OrganizationCreate,OrganizationUpdate
from datetime import datetime
from fastapi import HTTPException
from app.utils.logger import log


def get_all(db: Session, session_id: str = ""):
    try:
        log("organization-service", "INFO", "controller", "get_all", "Retrieving all organizations", session_id)

        organizations = db.query(Organization).all()

        log("organization-service", "INFO", "controller", "get_all", f"Successfully retrieved {len(organizations)} organizations", session_id)
        return organizations
    except Exception as e:
        log("organization-service", "ERROR", "controller", "get_all", f"Error retrieving organizations: {str(e)}", session_id)
        raise

def get_by_id(db: Session, id: int, session_id: str = ""):
    try:
        log("organization-service", "INFO", "controller", "get_by_id", f"Retrieving organization with ID: {id}", session_id)

        organization = db.query(Organization).filter(Organization.id == id).first()

        if organization:
            log("organization-service", "INFO", "controller", "get_by_id", f"Successfully retrieved organization: {organization.name}", session_id)
        else:
            log("organization-service", "INFO", "controller", "get_by_id", f"Organization with ID {id} not found", session_id)

        return organization
    except Exception as e:
        log("organization-service", "ERROR", "controller", "get_by_id", f"Error retrieving organization {id}: {str(e)}", session_id)
        raise

def get_by_user_id(db: Session, user_id: int, session_id: str = ""):
    try:
        log("organization-service", "INFO", "controller", "get_by_user_id", f"Retrieving organizations for user ID: {user_id}", session_id)

        # First, get the user to find their organization_id
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            log("organization-service", "INFO", "controller", "get_by_user_id", f"User with ID {user_id} not found", session_id)
            return []

        if not user.organization_id:
            log("organization-service", "INFO", "controller", "get_by_user_id", f"User {user_id} has no organization assigned", session_id)
            return []

        # Get the organization for this user
        organization = db.query(Organization).filter(Organization.id == user.organization_id).first()

        if organization:
            log("organization-service", "INFO", "controller", "get_by_user_id", f"Successfully retrieved organization for user {user_id}: {organization.name}", session_id)
            return [organization]  # Return as list for consistency with "all organizations" concept
        else:
            log("organization-service", "INFO", "controller", "get_by_user_id", f"Organization with ID {user.organization_id} not found for user {user_id}", session_id)
            return []

    except Exception as e:
        log("organization-service", "ERROR", "controller", "get_by_user_id", f"Error retrieving organizations for user {user_id}: {str(e)}", session_id)
        raise

def create(db: Session, org: OrganizationCreate, session_id: str = ""):
    try:
        log("organization-service", "INFO", "controller", "create", f"Creating new organization: {org.name}", session_id)

        # Check if org with same name already exists
        existing_org = db.query(Organization).filter(Organization.name == org.name).first()
        if existing_org:
            log("organization-service", "ERROR", "controller", "create", f"Organization already exists: {org.name}", session_id)
            raise HTTPException(status_code=400, detail="Organization already exists")

        # Proceed to create new organization
        new_org = Organization(
            name=org.name,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        db.add(new_org)
        db.commit()
        db.refresh(new_org)

        log("organization-service", "INFO", "controller", "create", f"Successfully created organization with ID: {new_org.id}", session_id)
        return new_org
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        db.rollback()
        log("organization-service", "ERROR", "controller", "create", f"Error creating organization: {str(e)}", session_id)
        raise

def update(db: Session, id: int, org: OrganizationUpdate, session_id: str = ""):
    try:
        log("organization-service", "INFO", "controller", "update", f"Updating organization with ID: {id}", session_id)

        db_org = get_by_id(db, id, session_id)
        if db_org:
            db_org.name = org.name
            db_org.updated_at = datetime.now()
            db.commit()
            db.refresh(db_org)

            log("organization-service", "INFO", "controller", "update", f"Successfully updated organization {id}: {org.name}", session_id)
        else:
            log("organization-service", "INFO", "controller", "update", f"Organization with ID {id} not found for update", session_id)

        return db_org
    except Exception as e:
        db.rollback()
        log("organization-service", "ERROR", "controller", "update", f"Error updating organization {id}: {str(e)}", session_id)
        raise

def delete(db: Session, id: int, session_id: str = ""):
    try:
        log("organization-service", "INFO", "controller", "delete", f"Deleting organization with ID: {id}", session_id)

        db_org = get_by_id(db, id, session_id)
        if db_org:
            org_name = db_org.name  # Store name for logging before deletion
            db.delete(db_org)
            db.commit()

            log("organization-service", "INFO", "controller", "delete", f"Successfully deleted organization {id}: {org_name}", session_id)
            return True
        else:
            log("organization-service", "INFO", "controller", "delete", f"Organization with ID {id} not found for deletion", session_id)
            return False
    except Exception as e:
        db.rollback()
        log("organization-service", "ERROR", "controller", "delete", f"Error deleting organization {id}: {str(e)}", session_id)
        raise